#!/usr/bin/python
# CS 6250 Fall 2024- SDN Firewall Project with POX
# build hackers-45

from pox.core import core
import pox.openflow.libopenflow_01 as of
import pox.lib.packet as pkt
from pox.lib.revent import *
from pox.lib.addresses import IPAddr, EthAddr

# You may use this space before the firewall_policy_processing function to add any extra function that you 
# may need to complete your firewall implementation.  No additional functions "should" be required to complete
# this assignment.


def firewall_policy_processing(policies):
    '''
    This is where you are to implement your code that will build POX/Openflow Match and Action operations to
    create a dynamic firewall meeting the requirements specified in your configure.pol file.  Do NOT hardcode
    the IP/MAC Addresses/Protocols/Ports that are specified in the project description - this code should use
    the values provided in the configure.pol to implement the firewall.

    The policies passed to this function is a list of dictionary objects that contain the data imported from the
    configure.pol file.  The policy variable in the "for policy in policies" represents a single line from the
    configure.pol file.  Each of the configuration values are then accessed using the policy['field'] command. 
    The fields are:  'rulenum','action','mac-src','mac-dst','ip-src','ip-dst','ipprotocol','port-src','port-dst',
    'comment'.

    Your return from this function is a list of flow_mods that represent the different rules in your configure.pol file.

    Implementation Hints:
    The documentation for the POX controller is available at https://noxrepo.github.io/pox-doc/html .  This project
    is using the gar-experimental branch of POX in order to properly support Python 3.  To complete this project, you
    need to use the OpenFlow match and flow_modification routines (https://noxrepo.github.io/pox-doc/html/#openflow-messages
    for flow_mod and https://noxrepo.github.io/pox-doc/html/#match-structure for match.)  Also, do NOT wrap IP Addresses with
    IPAddr() unless you reformat the CIDR notation.  Look at the https://github.com/att/pox/blob/master/pox/lib/addresses.py
    for what POX is expecting as an IP Address.
    '''

    rules = []

    for policy in policies:
        # Create a match object for this policy
        match = of.ofp_match()

        # Set priority - Allow rules should have higher priority than Block rules
        # Higher priority number = higher priority in OpenFlow
        if policy['action'] == 'Allow':
            priority = 1000 + int(policy['rulenum'])  # High priority for Allow rules
        else:
            priority = 500 + int(policy['rulenum'])   # Lower priority for Block rules

        # Handle MAC addresses
        if policy['mac-src'] != '-':
            match.dl_src = EthAddr(policy['mac-src'])
        if policy['mac-dst'] != '-':
            match.dl_dst = EthAddr(policy['mac-dst'])

        # Handle IP addresses with CIDR notation
        if policy['ip-src'] != '-':
            ip_src_addr = policy['ip-src-address']
            ip_src_subnet = int(policy['ip-src-subnet'])
            match.nw_src = IPAddr(ip_src_addr)
            if ip_src_subnet < 32:
                # Calculate netmask from CIDR notation
                netmask = (0xFFFFFFFF << (32 - ip_src_subnet)) & 0xFFFFFFFF
                match.nw_src = (IPAddr(ip_src_addr), netmask)

        if policy['ip-dst'] != '-':
            ip_dst_addr = policy['ip-dst-address']
            ip_dst_subnet = int(policy['ip-dst-subnet'])
            match.nw_dst = IPAddr(ip_dst_addr)
            if ip_dst_subnet < 32:
                # Calculate netmask from CIDR notation
                netmask = (0xFFFFFFFF << (32 - ip_dst_subnet)) & 0xFFFFFFFF
                match.nw_dst = (IPAddr(ip_dst_addr), netmask)

        # Handle IP protocol
        if policy['ipprotocol'] != '-':
            match.nw_proto = int(policy['ipprotocol'])
            # Set dl_type to IP for protocol-specific rules
            match.dl_type = 0x0800  # IPv4

        # Handle ports (only for TCP/UDP)
        if policy['port-src'] != '-' and policy['ipprotocol'] in ['6', '17']:  # TCP or UDP
            match.tp_src = int(policy['port-src'])
            match.dl_type = 0x0800  # IPv4

        if policy['port-dst'] != '-' and policy['ipprotocol'] in ['6', '17']:  # TCP or UDP
            match.tp_dst = int(policy['port-dst'])
            match.dl_type = 0x0800  # IPv4

        # Create flow modification
        rule = of.ofp_flow_mod()
        rule.match = match
        rule.priority = priority

        # Set action based on policy
        if policy['action'] == 'Allow':
            # Allow: forward normally (add NORMAL action)
            rule.actions.append(of.ofp_action_output(port=of.OFPP_NORMAL))
        else:
            # Block: drop packet (no action = drop)
            pass

        print('Added Rule ',policy['rulenum'],': ',policy['comment'])
        #print(rule)   #Uncomment this to debug your "rule"
        rules.append(rule)

    return rules
