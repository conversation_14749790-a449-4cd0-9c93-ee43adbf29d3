# CS 6250 Fall 2024 - SDN Firewall Project with POX
# build hackers-45
#
# This file contains the rules for the firewall as specified in the Project Documentation.
#
# Rule Format:
# RuleNumber,Action,Source MAC,Destination MAC,Source IP,Destination IP,Protocol,Source Port,Destination Port,Comment/Note
# RuleNumber = this is a rule number to help you track a particular rule - it is not used in the firewall implementation
# Action = Block or Allow , Allow Rules need to take precedence over Block Rules
# Source / Destination MAC address in form of xx:xx:xx:xx:xx:xx
# Source / Destination IP Address in form of xxx.xxx.xxx.xxx/xx in CIDR notation
# Protocol = integer IP protocol number per IANA (0-254)
# Source / Destination Port = if Protocol is TCP or UDP, this is the Application Port Number per IANA
# Comment/Note = this is for your use in tracking rules.
#
# Any field not being used for a match should have a '-' character as it's entry (except for RuleNumber/Comment)
# Do not pad any of the entries (i.e., have a rule like:  1, Block, -, -,...)
#
# Warning:  For the IP address, you need to specify an appropriate network address
# that matches the subnet mask you are using.  For instance, if you want to use a /16
# subnet mask, then the IP address must be x.x.0.0.  For example, a proper address to
# reference a 192.168.10.x/24 network would be ************/24.  A single host
# is addressed as a single IP address with a /32.  In other words, the host bit for a
# subnet other than /32 must be 0.
#
# Firewall Rules Based on Test Cases:

# Block China network (cn*) TCP traffic to specific destinations
1,Block,-,-,*********/24,-,6,-,-,Block all TCP from China network
2,Allow,-,-,*********/32,-,6,-,-,Allow TCP from cn1
3,Allow,-,-,*********/32,-,6,112,-,Allow TCP from cn3 with source port 112
4,Allow,-,-,*********/32,-,6,198,-,Allow TCP from cn3 with source port 198
5,Allow,-,-,*********/32,-,6,323,-,Allow TCP from cn3 with source port 323

# Block specific TCP ports from China network
6,Block,-,-,*********/24,-,6,-,80,Block TCP port 80 from China network
7,Block,-,-,*********/24,-,6,-,443,Block TCP port 443 from China network
8,Block,-,-,*********/24,-,6,-,99,Block TCP port 99 from China network

# Allow UDP from China network
9,Allow,-,-,*********/24,-,17,-,-,Allow UDP from China network

# Block all traffic to/from cn5
10,Block,-,-,*********/32,-,-,-,-,Block all traffic from cn5
11,Block,-,-,-,*********/32,-,-,-,Block all traffic to cn5

# Block ICMP to/from China network except cn1
12,Block,-,-,*********/24,-,1,-,-,Block ICMP from China network
13,Block,-,-,-,*********/24,1,-,-,Block ICMP to China network

# Allow ICMP between HQ and other networks
14,Allow,-,-,10.0.0.0/24,-,1,-,-,Allow ICMP from HQ network
15,Allow,-,-,-,10.0.0.0/24,1,-,-,Allow ICMP to HQ network
16,Allow,-,-,**********/24,-,1,-,-,Allow ICMP from other1 network
17,Allow,-,-,*********/24,-,1,-,-,Allow ICMP from other2 network
18,Allow,-,-,-,**********/24,1,-,-,Allow ICMP to other1 network
19,Allow,-,-,-,*********/24,1,-,-,Allow ICMP to other2 network

# Block ICMP between US and other networks
20,Block,-,-,********/24,**********/24,1,-,-,Block ICMP from US to other1
21,Block,-,-,********/24,*********/24,1,-,-,Block ICMP from US to other2
22,Block,-,-,**********/24,********/24,1,-,-,Block ICMP from other1 to US
23,Block,-,-,*********/24,********/24,1,-,-,Block ICMP from other2 to US

# Block ICMP between UK and other networks except specific hosts
24,Block,-,-,*********/24,**********/24,1,-,-,Block ICMP from UK to other1
25,Block,-,-,*********/24,*********/24,1,-,-,Block ICMP from UK to other2
26,Block,-,-,**********/24,*********/24,1,-,-,Block ICMP from other1 to UK
27,Block,-,-,*********/24,*********/24,1,-,-,Block ICMP from other2 to UK

# Block ICMP between India and other networks
28,Block,-,-,*********/24,**********/24,1,-,-,Block ICMP from India to other1
29,Block,-,-,*********/24,*********/24,1,-,-,Block ICMP from India to other2
30,Block,-,-,**********/24,*********/24,1,-,-,Block ICMP from other1 to India
31,Block,-,-,*********/24,*********/24,1,-,-,Block ICMP from other2 to India

# Allow ICMP within HQ network and between HQ and India/UK
32,Allow,-,-,10.0.0.0/24,*********/24,1,-,-,Allow ICMP from HQ to India
33,Allow,-,-,*********/24,10.0.0.0/24,1,-,-,Allow ICMP from India to HQ
34,Allow,-,-,10.0.0.0/24,*********/24,1,-,-,Allow ICMP from HQ to UK
35,Allow,-,-,*********/24,10.0.0.0/24,1,-,-,Allow ICMP from UK to HQ

# Block UK to US ICMP except uk1
36,Block,-,-,*********/24,********/24,1,-,-,Block ICMP from UK to US
37,Allow,-,-,10.0.40.1/32,********/24,1,-,-,Allow ICMP from uk1 to US

# Special TCP port rules
38,Block,-,-,-,-,6,-,9520,Block TCP port 9520 from specific sources
39,Allow,-,-,**********/24,-,6,-,9520,Allow TCP port 9520 from other1
40,Allow,-,-,*********/24,-,6,-,9520,Allow TCP port 9520 from other2
41,Allow,-,-,10.0.0.0/24,-,6,-,9520,Allow TCP port 9520 from HQ
42,Allow,-,-,10.0.40.1/32,-,6,-,9520,Allow TCP port 9520 from uk1

# Allow UDP port 9520 from more sources
43,Allow,-,-,-,-,17,-,9520,Allow UDP port 9520 from all sources

# Block TCP port 800 from US network
44,Block,-,-,********/24,-,6,-,800,Block TCP port 800 from US network
45,Allow,-,-,********/24,-,17,-,800,Allow UDP port 800 from US network

# Block UDP port 25 to specific destinations
46,Block,-,-,-,********/24,17,-,25,Block UDP port 25 to US network
47,Block,-,-,-,10.0.0.0/24,17,-,25,Block UDP port 25 to HQ network
48,Block,-,-,-,*********/24,17,-,25,Block UDP port 25 to China network

# Allow TCP port 25 to specific destinations
49,Allow,-,-,-,********/24,6,-,25,Allow TCP port 25 to US network
50,Allow,-,-,-,10.0.0.0/24,6,-,25,Allow TCP port 25 to HQ network
51,Allow,-,-,-,*********/24,6,-,25,Allow TCP port 25 to China network

# Allow specific ports to HQ
52,Allow,-,-,-,10.0.0.0/24,6,-,1194,Allow TCP port 1194 to HQ
53,Allow,-,-,-,10.0.0.0/24,17,-,1194,Allow UDP port 1194 to HQ
54,Allow,-,-,-,10.0.0.0/24,6,-,853,Allow TCP port 853 to HQ
55,Allow,-,-,-,10.0.0.0/24,17,-,853,Allow UDP port 853 to HQ

# Allow specific TCP ports from UK to US
56,Allow,-,-,*********/24,********/24,6,-,8510,Allow TCP port 8510 from UK to US

# Allow specific TCP ports
57,Allow,-,-,-,-,6,-,3389,Allow TCP port 3389
58,Allow,-,-,-,-,6,-,5900,Allow TCP port 5900

# Default allow rule for remaining traffic
59,Allow,-,-,-,-,-,-,-,Default allow all other traffic
